package cn.ac.picb.ipac.service;

import cn.ac.picb.ipac.common.core.ServiceException;
import cn.ac.picb.ipac.common.enums.DirectoryEnum;
import cn.ac.picb.ipac.common.enums.TaskStatusEnum;
import cn.ac.picb.ipac.common.enums.TaskType;
import cn.ac.picb.ipac.common.enums.venas.VenasTaskStatusEnum;
import cn.ac.picb.ipac.common.util.*;
import cn.ac.picb.ipac.config.Constants;
import cn.ac.picb.ipac.config.venas.VenasProperties;
import cn.ac.picb.ipac.dto.Venas1TaskDTO;
import cn.ac.picb.ipac.dto.Venas2TaskDTO;
import cn.ac.picb.ipac.dto.VenasTaskSearch;
import cn.ac.picb.ipac.model.TaskStatusFlow;
import cn.ac.picb.ipac.model.User;
import cn.ac.picb.ipac.model.UserTask;
import cn.ac.picb.ipac.mq.ipp.AnalysisTask;
import cn.ac.picb.ipac.mq.venas.Venas1Task;
import cn.ac.picb.ipac.mq.venas.Venas2Task;
import cn.ac.picb.ipac.repository.TaskStatusFlowRepository;
import cn.ac.picb.ipac.repository.UserTaskRepository;
import cn.ac.picb.ipac.vo.*;
import cn.ac.picb.ipac.vo.venas.VenasTaskVO;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.*;

@Service
@Slf4j
public class TaskService {
    @Autowired
    private UserTaskRepository userTaskRepository;
    @Autowired
    private TaskStatusFlowRepository taskStatusFlowRepository;
    @Autowired
    private VenasProperties venasProperties;

    /**
     * 任务列表
     *
     * @param user
     * @param pageable
     * @return
     */
    public Page<UserTaskVo> findTaskPage(User user, Pageable pageable, UserTaskSearch search) {
        Page<UserTask> page = userTaskRepository.findAll((root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            if (StringUtils.isNoneBlank(search.getTaskId())) {
                predicateList.add(cb.equal(root.get("taskId"), search.getTaskId()));
            }
            predicateList.add(cb.equal(root.get("type"), TaskType.ipp.name()));
            if (StringUtils.isNoneBlank(search.getStatusDesc())) {
                List<Integer> codes = TaskStatusEnum.getCodesByDesc(search.getStatusDesc());
                Path<Integer> p = root.get("status");
                CriteriaBuilder.In<Integer> in = cb.in(p);
                for (Integer code : codes) {
                    in.value(code);
                }
                predicateList.add(cb.or(in));
            }
            if (search.getCreateTimeStart() != null) {
                predicateList.add(cb.greaterThanOrEqualTo(root.get("createTime"), DateUtils.parseDate(DateUtils.formatDate(search.getCreateTimeStart(), "yyyy-MM-dd") + " 00:00:00")));
            }
            if (search.getCreateTimeEnd() != null) {
                predicateList.add(cb.lessThanOrEqualTo(root.get("createTime"), DateUtils.parseDate(DateUtils.formatDate(search.getCreateTimeEnd(), "yyyy-MM-dd") + " 23:59:59")));
            }
            predicateList.add(cb.equal(root.get("user").get("id"), user.getId()));
            predicateList.add(cb.notEqual(root.get("status"), TaskStatusEnum.delete.getCode()));
            query.where(predicateList.toArray(new Predicate[]{}));
            query.orderBy(cb.desc(root.get("createTime")));
            return null;
        }, pageable);
        List<UserTask> content = page.getContent();
        List<UserTaskVo> voList = new ArrayList<>();
        for (UserTask userTask : content) {
            UserTaskVo vo = new UserTaskVo();
            voList.add(vo);
            vo.setUserTask(userTask);
            vo.setTaskStatusFlow(getSameTaskStatusFlow(userTask));
            vo.setFiles(getFtpFileVoList(userTask, user));
            if (vo.getTaskStatusFlow() != null) {
                vo.setUseTime(getUseTime(userTask.getCreateTime(), vo.getTaskStatusFlow().getOperateTime()));
            }
        }
        return new PageImpl<>(voList, pageable, page.getTotalElements());
    }

    public String getUseTime(Date start, Date end) {
        if (start == null || end == null) {
            return null;
        }
        long time = (end.getTime() - start.getTime()) / 1000;
        if (time < 60) {
            return time + "s";
        } else if (time >= 60 && time < 3600) {
            String min = time / 60 + "m";
            String second = time % 60 == 0 ? "" : time % 60 + "s";
            return min + second;
        } else if (time >= 3600) {
            String useTime = time / 3600 + "h";
            long minLong = time % 3600;
            if (minLong != 0) {
                String min = minLong / 60 + "m";
                String second = minLong % 60 == 0 ? "" : time % 60 + "s";
                useTime += min + second;
            }
            return useTime;
        }
        return null;
    }

    public List<List<String>> getFtpFileVoList(UserTask userTask, User user) {
        return (List<List<String>>) JSONObject.parse(userTask.getInputPath());
    }

    public TaskStatusFlow getSameTaskStatusFlow(UserTask userTask) {
        if (CollectionUtils.isNotEmpty(userTask.getTaskStatusFlows())) {
            for (TaskStatusFlow taskStatusFlow : userTask.getTaskStatusFlows()) {
                if (Objects.equals(taskStatusFlow.getStatus(), userTask.getStatus())) {
                    return taskStatusFlow;
                }
            }
        }
        return null;
    }

    /**
     * 删除任务
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteTask(String id) {
        if (StringUtils.isBlank(id)) {
            throw new ServiceException("data exception");
        }
        UserTask userTask = userTaskRepository.findById(id).orElseThrow(() -> new ServiceException("The task does not exist！"));
        userTask.setStatus(TaskStatusEnum.delete.getCode());
        userTaskRepository.saveAndFlush(userTask);
    }

    /**
     * 重新分析任务（新建任务  通过pid pid_tree绑定关系 ）
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public AnalysisTask reanalysis(String id, User user) {
        if (StringUtils.isBlank(id)) {
            throw new ServiceException("data exception");
        }
        UserTask userTask = userTaskRepository.findById(id).orElseThrow(() -> new ServiceException("The task does not exist！"));
        UserTask userTaskN = new UserTask();
        userTaskN.setId(IdUtil.getShortUUID());
        userTaskN.setStatus(TaskStatusEnum.draft.getCode());
        userTaskN.setTaskId(getTaskId());
        userTaskN.setInputPath(userTask.getInputPath());
        Date date = new Date();
        userTaskN.setCreateTime(date);
        userTaskN.setModifyTime(date);
        userTaskN.setPid(userTask.getId());
        if (StringUtils.isNotBlank(userTask.getPidTree())) {
            userTaskN.setPidTree(userTask.getPidTree() + "," + userTaskN.getId());
        } else {
            userTaskN.setPidTree(userTask.getId() + "," + userTaskN.getId());
        }
        userTaskN.setUser(userTask.getUser());
        userTaskN.setType(StrUtil.isBlank(userTask.getType()) ? "ipp" : userTask.getType());
        userTaskRepository.saveAndFlush(userTaskN);
        //状态流
        addTaskStatusFlow(TaskStatusEnum.draft, userTaskN);

        return wrapAnalysisTask(userTaskN, user);
    }

    /**
     * 消息发布参数对象
     *
     * @param userTask
     * @return
     */
    private AnalysisTask wrapAnalysisTask(UserTask userTask, User user) {
        AnalysisTask analysisTask = new AnalysisTask();
        analysisTask.setTaskId(userTask.getTaskId());
        analysisTask.setType(userTask.getType());
        List<List<String>> fileIdArr = (List<List<String>>) JSONObject.parse(userTask.getInputPath());
        List<List<String>> pathArr = new ArrayList<>();
        for (List<String> list : fileIdArr) {
            List<String> arr = new ArrayList<>();
            for (String path : list) {
                arr.add(user.getAccountName() + (path.startsWith("/") ? path : "/" + path));
            }
            pathArr.add(arr);
        }
        analysisTask.setFiles(pathArr);
        return analysisTask;
    }

    /**
     * 获取任务号
     *
     * @return
     */
    public String getTaskId() {
        String taskId = DateUtils.formatDate(new Date(), "yyMMddHHmmssSSS");
        if (userTaskRepository.existsByTaskId(taskId)) {
            return getTaskId();
        }
        return taskId;
    }

    /**
     * 病毒分析-添加任务
     *
     * @param user
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public AnalysisTask addTask(User user, TaskFromVo vo) {
        UserTask userTask = new UserTask();
        userTask.setId(IdUtil.getShortUUID());
        userTask.setTaskId(getTaskId());
        userTask.setUser(user);
        Date date = new Date();
        userTask.setType(vo.getType());
        userTask.setCreateTime(date);
        userTask.setModifyTime(date);
        userTask.setStatus(TaskStatusEnum.draft.getCode());
        JSONArray jsonArray = new JSONArray();
        for (TaskFromVo.UserTaskIdArr userTaskIdArr : vo.getUserTaskIdArrList()) {
            List<String> ids = userTaskIdArr.getIds();
            if (ids == null || ids.size() != 2) {
                throw new ServiceException("data exception");
            }
            jsonArray.add(ids);
        }
        userTask.setInputPath(jsonArray.toJSONString());

        userTaskRepository.saveAndFlush(userTask);
        //状态流
        addTaskStatusFlow(TaskStatusEnum.draft, userTask);
        return wrapAnalysisTask(userTask, user);
    }

    /**
     * 任务状态流
     *
     * @param taskStatusEnum
     * @param userTask
     */
    private void addTaskStatusFlow(TaskStatusEnum taskStatusEnum, UserTask userTask) {
        TaskStatusFlow taskStatusFlow = new TaskStatusFlow();
        taskStatusFlow.setId(IdUtil.getShortUUID());
        taskStatusFlow.setStatus(taskStatusEnum.getCode());
        taskStatusFlow.setOperateTime(new Date());
        taskStatusFlow.setTaskId(userTask.getTaskId());
        taskStatusFlow.setUserTask(userTask);
        taskStatusFlowRepository.saveAndFlush(taskStatusFlow);
    }

    public void addTaskStatusFlow(Integer code, UserTask userTask) {
        TaskStatusFlow taskStatusFlow = new TaskStatusFlow();
        taskStatusFlow.setId(IdUtil.getShortUUID());
        taskStatusFlow.setStatus(code);
        taskStatusFlow.setOperateTime(new Date());
        taskStatusFlow.setTaskId(userTask.getTaskId());
        taskStatusFlow.setUserTask(userTask);
        taskStatusFlowRepository.saveAndFlush(taskStatusFlow);
    }

    public FtpFile findFtpFileById(String filePath, User user) {

        return null;
    }

    /**
     * 我的文件-文件列表
     *
     * @param user
     * @param parentPath
     * @return
     */
    public List<FtpFileVo> findFtpFiles(User user, String parentPath) {
        //todo
/*
        List<FtpFileVo> voList = new ArrayList<>();

        String token = AESUtil.encrypt(user.getFtpName() + "::" + user.getPassword(), Charset.defaultCharset(), Constants.secretKey);
        ApiResult<List<FtpFile>> result = ftpRemoteService.listUserDirectoryFiles(token, parentPath);
        if (result == null) {
            throw new ServiceException("List directory error");
        }
        if (result.getCode() != 200) {
            throw new ServiceException("List directory error: " + result.getMessage());
        }
        List<FtpFile> list = result.getData();
        if (CollectionUtils.isNotEmpty(list)) {
            for (FtpFile ftpFile : list) {
                FtpFileVo vo = new FtpFileVo();
                vo.setFtpFile(ftpFile);
                vo.setSize(FIleSizeUtil.sizeFormat(ftpFile.getFileSize()));
                voList.add(vo);
            }
        }
        return voList;*/
        return null;
    }

    /**
     * 删除文件
     *
     * @param id
     * @param user
     */
    public void deleteFile(String id, User user) {
        //todo
        /*        *//**
         * 此token是原始密码通过AES加密的，因为登录ftpClient需要原始密码
         *//*
        ApiResult<String> result = ftpRemoteService.deleteUserFiles(user.getFtpToken(), new String[]{id});
        if (result == null) {
            throw new ServiceException("Delete error");
        }
        if (result.getCode() != 200) {
            throw new ServiceException("Delete error: " + result.getMessage());
        }*/
    }

    public UserTask findUserTaskById(String id) {
        if (StringUtils.isBlank(id)) {
            throw new ServiceException("data exception！");
        }
        UserTask userTask = userTaskRepository.findById(id).orElseThrow(() -> new ServiceException("Task does not exist！：" + id));
        return userTask;
    }

    /**
     * 更新任务进度
     *
     * @param taskId taskId
     * @param status 任务状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateTaskStatus(String taskId, Integer status) {
        userTaskRepository.findByTaskId(taskId).ifPresent(userTask -> {
            if (TaskStatusEnum.delete.getCode().equals(userTask.getStatus())) {
                return;
            }
            TaskStatusEnum statusEnum = TaskStatusEnum.getEnum(status);
            if (statusEnum == null) {
                statusEnum = TaskStatusEnum.error;
                log.error("接收消息状态code 错误，错误code 为：{}", status);
                userTask.setErrMsg("任务状态错误");
            }

            userTask.setModifyTime(new Date());
            userTask.setStatus(statusEnum.getCode());

            addTaskStatusFlow(statusEnum, userTask);
        });
    }

    /**
     * 分析出错
     *
     * @param taskId taskId
     * @param msg    错误原因
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateTaskErrorStatus(String taskId, String msg) {
        userTaskRepository.findByTaskId(taskId).ifPresent(userTask -> {
            if (TaskStatusEnum.delete.getCode().equals(userTask.getStatus())) {
                return;
            }
            userTask.setModifyTime(new Date());
            userTask.setErrMsg(msg);
            userTask.setStatus(TaskStatusEnum.error.getCode());
            userTaskRepository.save(userTask);

            addTaskStatusFlow(TaskStatusEnum.error, userTask);
        });
    }

    /**
     * 分析完成，并发送用户提醒
     *
     * @param taskId taskId
     * @param path   path
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateTaskComplete(String taskId, String path) {
        userTaskRepository.findByTaskId(taskId).ifPresent(userTask -> {
            if (TaskStatusEnum.delete.getCode().equals(userTask.getStatus())) {
                return;
            }

            userTask.setModifyTime(new Date());
            userTask.setOutPath(path);
            userTask.setStatus(TaskStatusEnum.complete.getCode());

            log.info("complete 任务状态更新完成");
            userTaskRepository.save(userTask);

            addTaskStatusFlow(TaskStatusEnum.complete, userTask);

//            String email = userTask.getUser().getEmail();
//            log.debug("发送邮件提醒，email: {}", email);
//            if (StringUtils.isNotBlank(email)) {
//                String content = "您好，您提交的任务号为" + userTask.getTaskId() + "的任务已分析完成，请登录病毒基因组自动化鉴定云平台(HPC)版平台-个人中心-我的分析任务<a href=\"" + Constants.baseUrl + "\">下载</a>分析结果";
//                mailService.sendHtmlMail(email, "病毒基因组自动化鉴定云平台(HPC)版平台", content);
//            }
        });
    }

    public void downLoadResult(UserTask userTask, HttpServletResponse response, HttpServletRequest request) {
        File taskResultFile = new File(Constants.analysisResultHome, userTask.getOutPath());
        if (!taskResultFile.exists()) {
            throw new ServiceException("Result does not exist！");
        }
        if (!taskResultFile.isDirectory()) {
            throw new ServiceException("data exception！");
        }
        //临时文件
        File dataHome = FileUtils.getDataHome(DirectoryEnum.temp);
        File descFile = new File(dataHome, userTask.getTaskId());
        try {
            FileUtils.copyDirectory(taskResultFile, descFile);
        } catch (Exception e) {
            log.error("file copy failed");
            e.printStackTrace();
        }
        String filePath = descFile.getPath();
        try {
            //压缩
            ZipUtil.zipFiles(filePath, filePath + ".zip");
            DownloadUtils.download(request, response, new File(filePath + ".zip"));
        } catch (Exception e) {
            log.error("file download failed");
            e.printStackTrace();
        } finally {
            File tempFile = new File(filePath);
            File zipFile = new File(filePath + ".zip");
            if (tempFile.exists()) {
                try {
                    FileUtils.deleteDirectory(tempFile);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (zipFile.exists()) {
                zipFile.delete();
            }
        }
    }

    public void downLoadPdfZip(UserTask userTask, HttpServletRequest request, HttpServletResponse response) {
        File taskResultFile = new File(Constants.analysisResultHome, userTask.getOutPath() + File.separator + "reports");

        if (!taskResultFile.exists()) {
            throw new ServiceException("Result does not exist！");
        }
        if (!taskResultFile.isDirectory()) {
            throw new ServiceException("data exception！");
        }
        //临时文件
        File dataHome = FileUtils.getDataHome(DirectoryEnum.temp);
        File descFile = new File(dataHome, userTask.getTaskId());
        if (!descFile.exists()) {
            descFile.mkdirs();
        }
        int pdfCount = 0;
        File onalyOnePdfFile = null;
        try {
            //保持原有目录结果，只保留reportofvirus.pdf
            for (File file : taskResultFile.listFiles()) {
                if (!file.isDirectory()) {
                    continue;
                }
                File pdfFile = new File(file.getPath(), "reportofvirus.pdf");
                if (!pdfFile.exists()) {
                    continue;
                }

                File pdfCopyDirectory = new File(descFile.getPath(), file.getName());
                if (!pdfCopyDirectory.exists()) {
                    descFile.mkdirs();
                }
                FileUtils.copyFileToDirectory(pdfFile, pdfCopyDirectory);
                pdfCount++;
                onalyOnePdfFile = pdfFile;
            }
        } catch (Exception e) {
            log.error("file copy failed");
            e.printStackTrace();
        }
        String filePath = descFile.getPath();
        try {
            //压缩
            ZipUtil.zipFiles(filePath, filePath + ".zip");
            //只有一个pdf就直接下载
            if (pdfCount == 1 && onalyOnePdfFile != null && onalyOnePdfFile.exists()) {
                DownloadUtils.download(request, response, onalyOnePdfFile);
            } else {
                DownloadUtils.download(request, response, new File(filePath + ".zip"));
            }
        } catch (Exception e) {
            log.error("file download failed");
            e.printStackTrace();
        } finally {
            File tempFile = new File(filePath);
            File zipFile = new File(filePath + ".zip");
            if (tempFile.exists()) {
                try {
                    FileUtils.deleteDirectory(tempFile);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (zipFile.exists()) {
                zipFile.delete();
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Venas1Task venas1AddTask(User user, Venas1TaskDTO dto) {
        UserTask userTask = new UserTask();
        userTask.setId(IdUtil.getShortUUID());
        userTask.setTaskId("T" + getTaskId());
        userTask.setUser(user);
        Date date = new Date();
        userTask.setType(TaskType.venas1.name());
        userTask.setCreateTime(date);
        userTask.setModifyTime(date);
        userTask.setStatus(VenasTaskStatusEnum.draft.getCode());
        userTask.setOutPath("venas2_output_" + userTask.getTaskId());
        userTask.setAttr1(dto.getPathogenSpecies());
        JSONObject jsonObject = saveVenasFile(dto.getFile(), userTask.getTaskId() + ".txt");
        userTask.setInputPath(jsonObject.toJSONString());
        userTask.setAttr2(dto.getParameters());
        userTask.setAttr3(dto.getInitialSize());
        userTask.setAttr4(dto.getBatchSize());
        userTaskRepository.saveAndFlush(userTask);
        //状态流
        /*        addTaskStatusFlow(TaskStatusEnum.draft, userTask);*/
        Venas1Task task = new Venas1Task();
        task.setTaskId(userTask.getTaskId());
        return task;
    }

    @Transactional(rollbackFor = Exception.class)
    public Venas2Task venas2AddTask(User user, Venas2TaskDTO dto) {
        Optional<UserTask> parentTask = userTaskRepository.findByTaskId(dto.getParentTaskId());
        if (!parentTask.isPresent()) {
            throw new ServiceException("task is not exists");
        }
        UserTask userTask = new UserTask();
        userTask.setId(IdUtil.getShortUUID());
        userTask.setTaskId("T" + getTaskId());
        userTask.setUser(user);
        Date date = new Date();
        userTask.setType(TaskType.venas2.name());
        userTask.setCreateTime(date);
        userTask.setModifyTime(date);
        userTask.setStatus(VenasTaskStatusEnum.draft.getCode());
        userTask.setAttr1(dto.getPathogenSpecies());
        JSONObject jsonObject = saveVenasFile(dto.getFile(), userTask.getTaskId() + ".txt");
        userTask.setInputPath(jsonObject.toJSONString());
        userTask.setOutPath("venas2_output_" + userTask.getTaskId());
        userTask.setPid(parentTask.get().getId());
        userTaskRepository.saveAndFlush(userTask);
        //状态流
        /*        addTaskStatusFlow(TaskStatusEnum.draft, userTask);*/
        Venas2Task task = new Venas2Task();
        task.setTaskId(userTask.getTaskId());
        task.setParentTaskId(dto.getParentTaskId());
        return task;
    }

    private JSONObject saveVenasFile(MultipartFile file, String fileName) {
        if (file == null) {
            throw new ServiceException("file is not exists");
        }
        String originalFilename = file.getOriginalFilename();
        String path = "rawdata" + "/" + fileName;
        File descFile = new File(venasProperties.getScriptParentPath(), path);
        if (!descFile.getParentFile().exists())
            descFile.getParentFile().mkdirs();
        try {
            file.transferTo(descFile);
        } catch (IllegalStateException | IOException e) {
            throw new ServiceException("Upload failed");
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("path", path);
        jsonObject.put("size", FileUtil.readableFileSize(file.getSize()));
        jsonObject.put("originalFilename", originalFilename);
        return jsonObject;
    }

    public Page<VenasTaskVO> findVenasTaskPage(User user, Pageable pageable, VenasTaskSearch search) {
        Page<UserTask> page = userTaskRepository.findAll((root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            if (StringUtils.isNoneBlank(search.getTaskId())) {
                predicateList.add(cb.equal(root.get("taskId"), search.getTaskId()));
            }
            if (StringUtils.isNoneBlank(search.getStatusDesc())) {
                List<Integer> codes = VenasTaskStatusEnum.getCodesByDesc(search.getStatusDesc());
                Path<Integer> p = root.get("status");
                CriteriaBuilder.In<Integer> in = cb.in(p);
                for (Integer code : codes) {
                    in.value(code);
                }
                predicateList.add(cb.or(in));
            }
            if (search.getCreateTimeStart() != null) {
                predicateList.add(cb.greaterThanOrEqualTo(root.get("createTime"), DateUtils.parseDate(DateUtils.formatDate(search.getCreateTimeStart(), "yyyy-MM-dd") + " 00:00:00")));
            }
            if (search.getCreateTimeEnd() != null) {
                predicateList.add(cb.lessThanOrEqualTo(root.get("createTime"), DateUtils.parseDate(DateUtils.formatDate(search.getCreateTimeEnd(), "yyyy-MM-dd") + " 23:59:59")));
            }
            predicateList.add(root.get("type").in(Arrays.asList(TaskType.venas1.name(), TaskType.venas2.name())));
            predicateList.add(cb.equal(root.get("user").get("id"), user.getId()));
            predicateList.add(cb.notEqual(root.get("status"), TaskStatusEnum.delete.getCode()));
            query.where(predicateList.toArray(new Predicate[]{}));
            query.orderBy(cb.desc(root.get("createTime")));
            return null;
        }, pageable);
        List<UserTask> content = page.getContent();
        List<VenasTaskVO> voList = new ArrayList<>();
        for (UserTask userTask : content) {
            VenasTaskVO vo = new VenasTaskVO();
            voList.add(vo);
            vo.setUserTask(userTask);
            vo.setTaskStatusFlow(getSameTaskStatusFlow(userTask));
            if (StrUtil.isNotBlank(userTask.getInputPath())) {
                vo.setInputPath(JSONObject.parseObject(userTask.getInputPath()));
            }
            if (vo.getTaskStatusFlow() != null) {
                vo.setUseTime(getUseTime(userTask.getCreateTime(), vo.getTaskStatusFlow().getOperateTime()));
            }
        }
        return new PageImpl<>(voList, pageable, page.getTotalElements());
    }

    public void venasDownLoadResult(UserTask userTask, HttpServletResponse response, HttpServletRequest request) {
        String path = "venas2_output_" + userTask.getTaskId();
        File taskResultFile = new File(venasProperties.getScriptParentPath(), path);
        if (!taskResultFile.exists()) {
            throw new ServiceException("Result does not exist！");
        }
        if (!taskResultFile.isDirectory()) {
            throw new ServiceException("data exception！");
        }
        //临时文件
        File dataHome = FileUtils.getDataHome(DirectoryEnum.temp);
        File descFile = new File(dataHome, userTask.getTaskId());
        try {
            FileUtils.copyDirectory(taskResultFile, descFile);
        } catch (Exception e) {
            log.error("file copy failed");
            e.printStackTrace();
        }
        String filePath = descFile.getPath();
        try {
            //压缩
            ZipUtil.zipFiles(filePath, filePath + ".zip");
            DownloadUtils.download(request, response, new File(filePath + ".zip"));
        } catch (Exception e) {
            log.error("file download failed");
            e.printStackTrace();
        } finally {
            File tempFile = new File(filePath);
            File zipFile = new File(filePath + ".zip");
            if (tempFile.exists()) {
                try {
                    FileUtils.deleteDirectory(tempFile);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (zipFile.exists()) {
                zipFile.delete();
            }
        }
    }

    public Venas1Task venas1Reanalysis(UserTask userTask, User user) {
        userTask.setStatus(VenasTaskStatusEnum.draft.getCode());
        userTaskRepository.save(userTask);
        Venas1Task task = new Venas1Task();
        task.setTaskId(userTask.getTaskId());
        return task;
    }

    public Venas2Task venas2Reanalysis(UserTask userTask, User user) {
        UserTask parentTask = userTaskRepository.findById(userTask.getPid()).get();
        userTask.setStatus(VenasTaskStatusEnum.draft.getCode());
        userTaskRepository.save(userTask);
        Venas2Task task = new Venas2Task();
        task.setTaskId(userTask.getTaskId());
        task.setParentTaskId(parentTask.getTaskId());
        return task;
    }

    public JSONArray search(String keyword, User user, Pageable pageable) {
        Page<UserTask> all = userTaskRepository.findAll((root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.or(
                    cb.like(root.get("taskId"), "%" + StrUtil.nullToDefault(keyword, "") + "%"),
                    cb.like(root.get("remark"), "%" + StrUtil.nullToDefault(keyword, "") + "%")));
            predicateList.add(cb.or(
                    cb.equal(root.get("user").get("id"), user.getId()),
                    cb.isNull(root.get("user").get("id"))));
            predicateList.add(root.get("type").in(TaskType.venas1.name(), TaskType.venas2.name()));
            predicateList.add(cb.notEqual(root.get("status"), TaskStatusEnum.delete.getCode()));
            query.orderBy(cb.asc(root.get("taskId")));
            query.where(predicateList.toArray(new Predicate[]{}));
            return null;
        }, pageable);
        JSONArray jsonArray = new JSONArray();
        for (UserTask task : all.getContent()) {
            String text = task.getTaskId();
            if (StrUtil.isNotBlank(task.getRemark())) {
                text += "【" + task.getRemark() + "】";
            }
            JSONObject jsonObject = new JSONObject().fluentPut("id", task.getTaskId())
                    .fluentPut("taskId", task.getTaskId())
                    .fluentPut("text", text)
                    .fluentPut("name", task.getTaskId())
                    .fluentPut("term", task.getTaskId());
            jsonArray.add(jsonObject);
        }
        return jsonArray;
    }

    public void downLoadParentData(UserTask userTask, HttpServletRequest request, HttpServletResponse response) throws IOException {
        File folder = new File(venasProperties.getScriptParentPath(), userTask.getOutPath());
        File parentDataFile = new File(folder, "parent_data.txt");
        DownloadUtils.download(request, response, parentDataFile);
    }

    public void saveRemark(String id, String remark, User user) {
        if (StringUtils.isBlank(id)) {
            throw new ServiceException("data exception");
        }
        UserTask userTask = userTaskRepository.findById(id).orElseThrow(() -> new ServiceException("The task does not exist！"));
        if (!StrUtil.equals(userTask.getUser().getId(), user.getId())) {
            return;
        }
        userTask.setRemark(remark);
        userTaskRepository.saveAndFlush(userTask);
    }
}
