<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{base/layout}">
<div class="wrapper py-0" layout:fragment="content">
    <div class="container-fluid">
        <div class="page-title-box py-3">
            <div class="row align-items-center">
                <div class="col-sm-12">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a th:href="@{/index}"><i class="mdi mdi-home-outline"></i></a>
                        </li>
                        <li class="breadcrumb-item active">GVAP</li>
                    </ol>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xl-12">
                <div class="card card-box">
                    <div class="card-body">
                        <h4 class="mt-0 header-title mb-3">GVAP</h4>
                        <div class="info-box">
                            <h6>Instruction</h6>
                            <p class="mb-1">GVAP (Genome Variation Analysis Platform) is a high-performance genomic
                                variant analysis tool specifically designed for bacterial and viral genome studies.
                                Users can input target genome sequences (bacteria or viruses) along with their
                                corresponding reference genome sequences, and the system employs high-precision
                                alignment algorithms to detect various types of genomic variations, including SNPs
                                (Single Nucleotide Polymorphisms), InDels (Insertions/Deletions), and Structural
                                Variants (SVs), while generating comprehensive variant annotation reports. This tool is
                                widely applicable in molecular epidemiological investigations, pathogen surveillance,
                                and vaccine/drug target research, providing a robust and comprehensive solution for
                                microbial genomic variant analysis.</p>
                        </div>
                        <form class="ana-form" id="dataForm">
                            <div class="row">
                                <div class="offset-md-1 col-md-10">
                                    <div class="card border shadow-sm">
                                        <div class="card-header bg-white">
                                            <div class="d-flex align-content-center justify-content-between">
                                                <h5 class="font-600 font-14 mt-2 mb-0">Select the fasta files</h5>
                                            </div>
                                        </div>
                                        <div class="card-body" id="file_clone_div">
                                            <div class="form-group row mb-2">
                                                <div class="col-sm-12 d-flex justify-content-center" id="submitFormDiv">
                                                    <div class="type-cont w-100">
                                                        <div class="text-back font-600 mb-1 mt-3">Fasta file list</div>
                                                        <div class="text-back mb-1 font-14 pl-3">Upload an excel file：
                                                        </div>
                                                        <div class="col-sm-12 checkbox-collapse">
                                                            <div class="d-flex mb-2 justify-content-center">
                                                                <input type="file" class="form-control form-control-sm w-50 mr-2">
                                                                <button onclick="uploadExcel()" type="button"
                                                                        class="btn btn-primary btn-sm mr-2 text-nowrap">
                                                                    Upload
                                                                </button>
                                                                <a href="javascript:void(0);" onclick="downloadExcel()"
                                                                   class="btn btn-link btn-sm text-nowrap"><i
                                                                        class="fa fa-download mr-1"></i>Download
                                                                    Template</a>
                                                            </div>
                                                            <div class="text-back mb-1 font-14 pl-1">or use built-in
                                                                table
                                                                editor:
                                                            </div>
                                                            <div>
                                                                <div id="m-1-1" class="table-responsive mb-2">
                                                                    <div class="mb-3">
                                                                        <label class="form-label font-weight-bold">Reference File</label>
                                                                        <table class="table table-bordered table-sm table-middle mb-0"
                                                                               id="refFileTable">
                                                                            <thead class="thead-light">
                                                                            <tr>
                                                                                <td width="150" class="text-center">Sample
                                                                                    Name
                                                                                </td>
                                                                                <td>Fasta file</td>
                                                                            </tr>
                                                                            </thead>
                                                                            <tbody class="data-tbody">
                                                                            <tr>
                                                                                <td class="td-input">
                                                                                    <input type="text"
                                                                                           class="form-control form-control-sm text-center rounded-0 sample_name">
                                                                                </td>
                                                                                <td>
                                                                                    <div class="d-flex flex-wrap">
                                                                                        <button class="btn btn-outline-secondary btn-sm"
                                                                                                type="button" onclick="showFileModal(this)">
                                                                                            Select
                                                                                        </button>
                                                                                        <div class="btn-group btn-group-sm ml-1 fq_file"></div>
                                                                                    </div>
                                                                                </td>
                                                                            </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </div>

                                                                    <div class="mb-3">
                                                                        <label class="form-label font-weight-bold">Input File</label>
                                                                        <table class="table table-bordered table-sm table-middle mb-0"
                                                                               id="selectFileTable">
                                                                            <thead class="thead-light">
                                                                            <tr>
                                                                                <td width="150" class="text-center">Sample
                                                                                    Name
                                                                                </td>
                                                                                <td>Fasta file</td>
                                                                                <td width="100"></td>
                                                                            </tr>
                                                                            </thead>
                                                                            <tbody class="data-tbody">
                                                                            <tr>
                                                                                <td class="td-input">
                                                                                    <input type="text"
                                                                                           class="form-control form-control-sm text-center rounded-0 sample_name">
                                                                                </td>
                                                                                <td>
                                                                                    <div class="d-flex flex-wrap">
                                                                                        <button class="btn btn-outline-secondary btn-sm"
                                                                                                type="button" onclick="showFileModal(this)">
                                                                                            Select
                                                                                        </button>
                                                                                        <div class="btn-group btn-group-sm ml-1 fq_file"></div>
                                                                                    </div>
                                                                                </td>
                                                                                <td>
                                                                                    <button type="button" class="btn btn-primary btn-sm"
                                                                                            onclick="addRow(this)">
                                                                                        <i class="fa fa-plus"></i>
                                                                                    </button>
                                                                                    <button type="button" class="btn btn-secondary btn-sm"
                                                                                            onclick="removeRow(this)"><i
                                                                                            class="fa fa-minus"></i>
                                                                                    </button>
                                                                                </td>
                                                                            </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                        <div class="card-footer bg-white">
                                            <div class="text-center">
                                                <button type="button" onclick="submitForm(this)" class="btn btn-primary mr-2">
                                                    Analysis
                                                </button>
                                                <button type="reset" class="btn btn-secondary" onclick="location.reload()">
                                                    Reset
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="file-modal"></div>
</div>
</html>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/jquery-ui.min.js}"></script>

    <script th:src="@{/js/jquery.fancytree-all.min.js}"></script>
    <script th:src="@{/js/jquery.fancytree.table.js}"></script>
    <script th:src="@{/js/file-modal.js}"></script>

    <script>
        $(document).ready(function () {
            $("#file-modal").fileModal('/file/fileTreeAjax');
        })

        // terminal btn
        var _selectBtn;

        // file btn
        var _selectFile;

        $("#file-modal").on('__SELECT_FILES__', function (e, data) {
            var nodes = data.nodes || []
            if (_selectFile) {
                if (nodes.length === 0) {
                    $(_selectFile).next().find('.fq_file').addClass("validate[required]");
                    return;
                }
                if (nodes.length > 1) {
                    layer.msg("only one file can be selected");
                    return;
                }

                var html = [];
                var filePath = nodes[0].path;
                var fileName = nodes[0].name;
                var fileSize = nodes[0].size;
                html.push('<div class="border rounded p-8 mx-1 font-14">');
                html.push('<input type="hidden" value="' + filePath + '">');
                html.push('<input type="hidden" value="' + fileName + '">');
                html.push('<input type="hidden" value="' + fileSize + '">');
                html.push('<span>' + fileName + '</span>');
                html.push('<span><a href="javascript:void(0);" onclick="deleteFile(this)" class="text-danger"><i class="fa fa-times-circle"></i></a></span>');
                html.push('</div>');
                $(_selectFile).next().html(html.join(''));
            } else {
                if (nodes.length === 0) {
                    $(_selectBtn).parent().next().find('em.seled:first').addClass("validate[required]");
                    return;
                }
                if (nodes.length > 1) {
                    layer.msg("only one file can be selected");
                    return;
                }
                var html = [];
                $.each(nodes, function (i, node) {
                    var filePath = node.path;
                    var fileName = node.name;
                    var fileSize = node.size;

                    html.push('<button type="button" class="btn btn-secondary">' + fileName + '</button>');
                    html.push('<input type="hidden" value="' + filePath + '">');
                    html.push('<input type="hidden" value="' + fileName + '">');
                    html.push('<input type="hidden" value="' + fileSize + '">');
                    html.push('<button type="button" class="btn btn-secondary" onclick="removeFile(this)"><i class="fa fa-times-circle"></i></button>');
                    html.push('</b>');
                });

                $(_selectBtn).next().html(html.join(''));
                $(_selectBtn).next().find('.fq_file').removeClass("validate[required]");
            }
        });

        function showFileModal(_this) {
            console.log(checkLogin())
            _selectBtn = _this;
            _selectFile = null;
            var selectIds = [];
            $(_this).next().find(".fq_file").each(function () {
                selectIds.push($(this).find(":input:eq(0)").val());
            });
            $("#file-modal").trigger('__SHOW_SELECT_FILES_MODAL__', {
                selectIds: selectIds,
                func: function (data) {
                    return !data.name || /.*\.fa/.test(data.name) || /.*\.fasta/.test(data.name);
                }
            });
        }

        function removeFile(_this) {
            $(_this).parent().html('');
        }

        function addRow(_this) {
            console.log(_this)

            var clone = $(_this).parents('tr:first').clone();
            clone.find('.sample_name').val('');
            clone.find('.fq_file').html('');
            $("#selectFileTable").find('tbody').append(clone);
        }

        function removeRow(_this) {
            var rows = $("#selectFileTable").find("tbody.data-tbody").find('tr');

            var rowDiv = $(_this).parents('tr:first');
            if (rows.length > 1) {
                rowDiv.remove();
            } else {
                rowDiv.find('.sample_name').val('');
                rowDiv.find('.fq_file').html('');
            }
            $("#check-result").val('false');
        }


        function checkLogin() {
            var username = $("#username").attr("username");
            if (username == undefined || username == '') {
                window.location.href = $("#loginA").attr('href');
                return false;
            }
            return true;
        }

        function submitForm(_this) {
            if (!checkLogin()) {
                return;
            }

            // 校验逻辑
            var formData = new FormData();
            var rows = $("#selectFileTable").find("tbody.data-tbody").find("tr");
            var refRow = $("#refFileTable").find("tbody.data-tbody").find("tr:eq(0)");

            // 校验Reference File
            if (refRow.length > 0) {
                var refSample = $(refRow).find('input:eq(0)').val();
                var refPath = $(refRow).find('input:eq(1)').val();
                var refName = $(refRow).find('input:eq(2)').val();
                var refSize = $(refRow).find('input:eq(3)').val();

                // 校验Reference File的sampleName不能为空
                if (!refSample || refSample.trim() === '') {
                    layer.msg("Reference File sample name cannot be empty");
                    return;
                }

                // 校验Reference File必须上传
                if (!refPath || refPath === '') {
                    layer.msg("Reference File must be uploaded");
                    return;
                }

                formData.append("refFile.sampleName", refSample);
                formData.append("refFile.file.path", refPath);
                formData.append("refFile.file.name", refName);
                formData.append("refFile.file.size", refSize);
            } else {
                layer.msg("Reference File must be uploaded");
                return;
            }

            // 收集所有sampleName用于重复检查
            var sampleNames = [];
            var refSampleName = $(refRow).find('input:eq(0)').val().trim();
            if (refSampleName) {
                sampleNames.push(refSampleName.toLowerCase());
            }

            // 校验Input Files
            var validInputFiles = [];
            var hasError = false;

            $.each(rows, function (i, item) {
                var sample = $(item).find('input:eq(0)').val();
                var path = $(item).find('input:eq(1)').val();
                var name = $(item).find('input:eq(2)').val();
                var size = $(item).find('input:eq(3)').val();

                // 如果这一行有任何数据，则进行校验
                if (sample || path) {
                    // 校验sampleName不能为空
                    if (!sample || sample.trim() === '') {
                        layer.msg("Input File sample name cannot be empty (row " + (i + 1) + ")");
                        hasError = true;
                        return false;
                    }

                    // 校验sampleName不能重复
                    var sampleLower = sample.trim().toLowerCase();
                    if (sampleNames.indexOf(sampleLower) !== -1) {
                        layer.msg("Sample name '" + sample.trim() + "' is duplicated");
                        hasError = true;
                        return false;
                    }
                    sampleNames.push(sampleLower);

                    // 校验文件必须上传
                    if (!path || path === '') {
                        layer.msg("Input File must be uploaded (row " + (i + 1) + ")");
                        hasError = true;
                        return false;
                    }

                    validInputFiles.push({
                        index: i,
                        sample: sample,
                        path: path,
                        name: name,
                        size: size
                    });
                }
            });

            if (hasError) {
                return;
            }

            // 至少需要一个Input File
            if (validInputFiles.length === 0) {
                layer.msg("At least one Input File must be uploaded");
                return;
            }

            // 添加有效的Input Files到formData
            $.each(validInputFiles, function (i, item) {
                formData.append("inputFiles[" + i + "].sampleName", item.sample);
                formData.append("inputFiles[" + i + "].file.path", item.path);
                formData.append("inputFiles[" + i + "].file.name", item.name);
                formData.append("inputFiles[" + i + "].file.size", item.size);
            });
            formData.forEach((value, key) => {
                console.log(key, value);
            });
            var loadLayerIndex;
            $.ajax({
                url: '/gvap/addTask',
                method: 'post',
                dataType: 'json',
                contentType: false,
                processData: false,
                data: formData,
                beforeSend: function () {
                    loadLayerIndex = layer.load(1, {
                        shade: [0.1, '#fff'] //0.1透明度的白色背景
                    });
                },
                success: function (result) {
                    if (result.code == 200) {
                        layer.msg("Submitted successfully", {time: 500}, function () {
                            var _context_path = $("meta[name='_context_path']").attr("content");
                            location.href = _context_path + "/usercenter/tasks";
                        });
                    } else {
                        layer.alert(result.message, {icon: 2});
                    }
                },
                complete: function () {
                    layer.close(loadLayerIndex);
                }
            });
        }

        function uploadExcel() {

            var tableDiv = $("#selectFileTable");
            var refTableDiv = $("#refFileTable");

            var fileInput = $('.type-cont').find("input[type=file]:eq(0)");

            if (fileInput.val() === '') {
                layer.msg('please select a excel file');
                return;
            }
            var formData = new FormData();
            formData.append("excel", $(fileInput)[0].files[0]);

            $.ajax({
                url: '/gvap/uploadTemplate',
                data: formData,
                dataType: 'json',
                type: 'post',
                async: false,
                processData: false,
                contentType: false,
                success: function (result) {
                    if (result.success) {
                        let refFile = result.data.refFile;
                        if (!refFile) {
                            layer.msg("data is empty");
                        }
                        let refHtmlArr = ['<tr>']
                        refHtmlArr.push('<td class="td-input"> <input type="text" class="form-control form-control-sm text-center rounded-0 sample_name" value="' + refFile.sampleName + '"></td>');
                        obtainTd(refHtmlArr, refFile.file);
                        refHtmlArr.push('</tr>');
                        $(refTableDiv).find("tbody").html(refHtmlArr.join(''));

                        var inputs = result.data.inputFiles || [];
                        if (inputs.length === 0) {
                            layer.msg("data is empty");
                            return;
                        }
                        var trs = [];
                        $.each(inputs, function (idx, item) {
                            var html = ['<tr>'];
                            html.push('<td class="td-input"> <input type="text" class="form-control form-control-sm text-center rounded-0 sample_name" value="' + item.sampleName + '"></td>');

                            obtainTd(html, item.file);
                            html.push('<td><button type="button" class="btn btn-primary btn-sm" onclick="addRow(this)"><i class="fa fa-plus"></i></button><button type="button" class="btn btn-secondary btn-sm" onclick="removeRow(this)"><i class="fa fa-minus"></i></button></td>')
                            html.push('</tr>');
                            trs.push(html.join(''))
                        })
                        $(tableDiv).find("tbody").html(trs.join(''));
                    } else {
                        layer.msg(result.message);
                    }
                }
            })
        }

        function downloadExcel() {
            window.location.href = '[[@{/gvap/downloadTemplateExcel}]]';
        }

        function obtainTd(html, node) {
            if (!node) {
                return;
            }
            var filePath = node.path;
            var fileName = node.name;
            var fileSize = node.size;

            html.push('<td>');
            html.push('<div class="d-flex flex-wrap">');
            html.push('<button class="btn btn-outline-secondary btn-sm" type="button" onclick="showFileModal(this)">Select</button>');

            html.push('<div class="btn-group btn-group-sm ml-1 fq_file">');
            html.push('<button type="button" class="btn btn-secondary">' + fileName + '</button>');
            html.push('<input type="hidden" value="' + filePath + '">');
            html.push('<input type="hidden" value="' + fileName + '">');
            html.push('<input type="hidden" value="' + fileSize + '">');
            html.push('<button type="button" class="btn btn-secondary" onclick="removeFile(this)"><i class="fa fa-times-circle"></i></button>');
            html.push('</div>');
            html.push('</td>');
        }


    </script>
</th:block>
