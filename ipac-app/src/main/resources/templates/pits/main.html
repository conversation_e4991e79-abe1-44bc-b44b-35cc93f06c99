<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{base/layout}">
<div class="wrapper py-0" layout:fragment="content">
    <div class="container-fluid">
        <div class="page-title-box py-3">
            <div class="row align-items-center">
                <div class="col-sm-12">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a th:href="@{/home}"><i class="mdi mdi-home-outline"></i></a>
                        </li>
                        <li class="breadcrumb-item active">PITS</li>
                    </ol>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xl-12">
                <div class="card card-box">
                    <div class="card-body">
                        <h4 class="mt-0 header-title mb-3">PITS</h4>
                        <div class="info-box">
                            <h6>Instruction</h6>
                            <p class="mb-1">
                                <img th:src="@{/images/pic-pits.png}" class="img-info" height="300" alt="">
                            </p>
                            <p class="mb-1">PITS (Pathogens Identification and Typing System) is a tool integrating
                                precise bacterial identification, strain typing, and downstream analysis for predicting
                                antimicrobial resistance (AMR) and virulence genes. Its identification capability is
                                based on a curated genomic database of bacterial reference strains. PITS employs a
                                comprehensive identification strategy designed to achieve rapid and precise pathogen
                                identification and analysis.</p>

                        </div>
                        <form class="ana-form" id="dataForm">
                            <div class="row">
                                <div class="offset-md-1 col-md-10">
                                    <div class="card border shadow-sm">
                                        <div class="card-header bg-white">
                                            <div class="d-flex align-content-center justify-content-between">
                                                <h5 class="font-600 font-14 mt-2 mb-0">Select the fasta file</h5>
                                            </div>
                                        </div>
                                        <div class="card-body" id="file_clone_div">
                                            <div class="form-group row mb-2">
                                                <div class="col-sm-12 d-flex justify-content-center" id="submitFormDiv">
                                                    <div class="type-cont w-100">
                                                        <div class="col-sm-12 checkbox-collapse">
                                                            <div>
                                                                <div id="m-1-1" class="table-responsive mb-2">
                                                                    <div class="mb-3">
                                                                        <label class="form-label font-weight-bold">Input
                                                                            File</label>
                                                                        <table class="table table-bordered table-sm table-middle mb-0"
                                                                               id="selectFileTable">
                                                                            <thead class="thead-light">
                                                                            <tr>
                                                                                <td width="150" class="text-center">
                                                                                    Sample
                                                                                    Name
                                                                                </td>
                                                                                <td>Fasta file</td>
                                                                            </tr>
                                                                            </thead>
                                                                            <tbody class="data-tbody">
                                                                            <tr>
                                                                                <td class="td-input">
                                                                                    <input type="text"
                                                                                           onkeyup="value=value.replace(/[^\w\.\/]/ig,'')"
                                                                                           class="form-control form-control-sm text-center rounded-0 sample_name"
                                                                                           placeholder="Enter sample name">
                                                                                </td>
                                                                                <td>
                                                                                    <div class="d-flex flex-wrap">
                                                                                        <button class="btn btn-outline-secondary btn-sm"
                                                                                                type="button" onclick="showFileModal(this)">
                                                                                            Select
                                                                                        </button>
                                                                                        <div class="btn-group btn-group-sm ml-1 fq_file"></div>
                                                                                    </div>
                                                                                </td>
                                                                            </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-footer bg-white">
                                            <div class="text-center">
                                                <button type="button" onclick="submitForm(this)" class="btn btn-primary mr-2">
                                                    Analysis
                                                </button>
                                                <button type="reset" class="btn btn-secondary" onclick="location.reload()">
                                                    Reset
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="file-modal"></div>
</div>
</html>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/jquery-ui.min.js}"></script>

    <script th:src="@{/js/jquery.fancytree-all.min.js}"></script>
    <script th:src="@{/js/jquery.fancytree.table.js}"></script>
    <script th:src="@{/js/file-modal.js}"></script>

    <script>
        $(document).ready(function () {
            $("#file-modal").fileModal('/file/fileTreeAjax');
        })

        // terminal btn
        var _selectBtn;

        // file btn
        var _selectFile;

        $("#file-modal").on('__SELECT_FILES__', function (e, data) {
            var nodes = data.nodes || []
            if (_selectFile) {
                if (nodes.length === 0) {
                    $(_selectFile).next().find('.fq_file').addClass("validate[required]");
                    return;
                }
                if (nodes.length > 1) {
                    layer.msg("only one file can be selected");
                    return;
                }

                var html = [];
                var filePath = nodes[0].path;
                var fileName = nodes[0].name;
                var fileSize = nodes[0].size;
                html.push('<div class="border rounded p-8 mx-1 font-14">');
                html.push('<input type="hidden" value="' + filePath + '">');
                html.push('<input type="hidden" value="' + fileName + '">');
                html.push('<input type="hidden" value="' + fileSize + '">');
                html.push('<span>' + fileName + '</span>');
                html.push('<span><a href="javascript:void(0);" onclick="deleteFile(this)" class="text-danger"><i class="fa fa-times-circle"></i></a></span>');
                html.push('</div>');
                $(_selectFile).next().html(html.join(''));
            } else {
                if (nodes.length === 0) {
                    $(_selectBtn).parent().next().find('em.seled:first').addClass("validate[required]");
                    return;
                }
                if (nodes.length > 1) {
                    layer.msg("only one file can be selected");
                    return;
                }
                var html = [];
                $.each(nodes, function (i, node) {
                    var filePath = node.path;
                    var fileName = node.name;
                    var fileSize = node.size;

                    html.push('<button type="button" class="btn btn-secondary">' + fileName + '</button>');
                    html.push('<input type="hidden" value="' + filePath + '">');
                    html.push('<input type="hidden" value="' + fileName + '">');
                    html.push('<input type="hidden" value="' + fileSize + '">');
                    html.push('<button type="button" class="btn btn-secondary" onclick="removeFile(this)"><i class="fa fa-times-circle"></i></button>');
                    html.push('</b>');
                });

                $(_selectBtn).next().html(html.join(''));
                $(_selectBtn).next().find('.fq_file').removeClass("validate[required]");
            }
        });

        function showFileModal(_this) {
            console.log(checkLogin())
            _selectBtn = _this;
            _selectFile = null;
            var selectIds = [];
            $(_this).next().find(".fq_file").each(function () {
                selectIds.push($(this).find(":input:eq(0)").val());
            });
            $("#file-modal").trigger('__SHOW_SELECT_FILES_MODAL__', {
                selectIds: selectIds,
                func: function (data) {
                    return !data.name || /\.fa$/.test(data.name) || /.*\.fasta/.test(data.name);
                }
            });
        }

        function removeFile(_this) {
            $(_this).parent().html('');
        }

        function checkLogin() {
            var username = $("#username").attr("username");
            if (username == undefined || username == '') {
                window.location.href = $("#loginA").attr('href');
                return false;
            }
            return true;
        }

        function submitForm(_this) {
            if (!checkLogin()) {
                return;
            }

            // 表单校验
            var rows = $("#selectFileTable").find("tbody.data-tbody").find("tr");

            // 收集所有sampleName用于重复检查
            var sampleNames = [];
            var validInputFiles = [];
            var hasError = false;

            // 遍历所有行进行校验
            $.each(rows, function (i, item) {
                var sample = $(item).find('input:eq(0)').val();
                var path = $(item).find('input:eq(1)').val();
                var name = $(item).find('input:eq(2)').val();
                var size = $(item).find('input:eq(3)').val();

                // 如果这一行有任何数据，则进行校验
                if (sample || path) {
                    // 校验样本名称不能为空
                    if (!sample || sample.trim() === '') {
                        layer.msg("Sample name cannot be empty (row " + (i + 1) + ")", {icon: 2});
                        $(item).find('input:eq(0)').focus();
                        hasError = true;
                        return false; // 跳出循环
                    }

                    // 校验样本名称不能重复
                    var sampleLower = sample.trim().toLowerCase();
                    if (sampleNames.indexOf(sampleLower) !== -1) {
                        layer.msg("Sample name '" + sample.trim() + "' is duplicated", {icon: 2});
                        $(item).find('input:eq(0)').focus();
                        hasError = true;
                        return false; // 跳出循环
                    }
                    sampleNames.push(sampleLower);

                    // 校验文件必须上传
                    if (!path || path === '') {
                        layer.msg("Fasta file must be selected (row " + (i + 1) + ")", {icon: 2});
                        hasError = true;
                        return false; // 跳出循环
                    }

                    validInputFiles.push({
                        index: i,
                        sample: sample.trim(),
                        path: path,
                        name: name,
                        size: size
                    });
                }
            });

            // 如果有校验错误，停止提交
            if (hasError) {
                return;
            }

            // 至少需要一个有效的输入文件
            if (validInputFiles.length === 0) {
                layer.msg("Please enter sample name and select fasta file", {icon: 2});
                return;
            }

            var formData = new FormData();
            // 添加有效的输入文件到formData
            $.each(validInputFiles, function (i, item) {
                formData.append("inputFile.sampleName", item.sample);
                formData.append("inputFile.file.path", item.path);
                formData.append("inputFile.file.name", item.name);
                formData.append("inputFile.file.size", item.size);
            })

            formData.forEach((value, key) => {
                console.log(key, value);
            });
            var loadLayerIndex;
            $.ajax({
                url: '/pits/addTask',
                method: 'post',
                dataType: 'json',
                contentType: false,
                processData: false,
                data: formData,
                beforeSend: function () {
                    loadLayerIndex = layer.load(1, {
                        shade: [0.1, '#fff'] //0.1透明度的白色背景
                    });
                },
                success: function (result) {
                    if (result.code == 200) {
                        layer.msg("Submitted successfully", {time: 500}, function () {
                            var _context_path = $("meta[name='_context_path']").attr("content");
                            location.href = _context_path + "/usercenter/tasks";
                        });
                    } else {
                        layer.alert(result.message, {icon: 2});
                    }
                },
                complete: function () {
                    layer.close(loadLayerIndex);
                }
            });
        }


    </script>
</th:block>
